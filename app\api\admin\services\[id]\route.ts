import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth, logAdminAction } from '@/lib/admin-auth'
import { Database } from '@/lib/types/database'

type Service = Database['public']['Tables']['services']['Row']
type ServiceUpdate = Database['public']['Tables']['services']['Update']

// GET /api/admin/services/[id] - Get single service with full details
export const GET = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id

    const { data: service, error } = await supabaseAdmin
      .from('services')
      .select(`
        *,
        pricing_tiers (
          id,
          tier_name,
          price,
          min_age,
          max_age,
          is_active,
          created_at,
          updated_at
        ),
        service_equipment_requirements (
          id,
          capacity_per_participant,
          equipment (
            id,
            name,
            description,
            total_capacity,
            is_active
          )
        ),
        service_scheduling_rules (
          id,
          day_of_week,
          min_advance_booking_hours,
          max_advance_booking_days,
          operating_start_time,
          operating_end_time,
          booking_interval_minutes,
          specific_times,
          max_bookings_per_day,
          is_active,
          created_at,
          updated_at
        ),
        service_blackout_dates (
          id,
          start_date,
          end_date,
          reason,
          is_active,
          created_at,
          updated_at
        ),
        employee_service_qualifications (
          id,
          employee_id,
          qualification_level,
          certified_date,
          expiry_date,
          notes,
          is_active,
          employees (
            id,
            first_name,
            last_name,
            email,
            role
          )
        )
      `)
      .eq('id', serviceId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Service not found' }, { status: 404 })
      }
      console.error('Error fetching service:', error)
      return NextResponse.json({ error: 'Failed to fetch service' }, { status: 500 })
    }

    // Get reservation statistics
    const { data: reservationStats } = await supabaseAdmin
      .from('reservations')
      .select('status, created_at')
      .eq('service_id', serviceId)

    const stats = {
      totalReservations: reservationStats?.length || 0,
      confirmedReservations: reservationStats?.filter(r => r.status === 'confirmed').length || 0,
      pendingReservations: reservationStats?.filter(r => r.status === 'pending').length || 0,
      cancelledReservations: reservationStats?.filter(r => r.status === 'cancelled').length || 0,
      thisMonthReservations: reservationStats?.filter(r => {
        const reservationDate = new Date(r.created_at)
        const now = new Date()
        return reservationDate.getMonth() === now.getMonth() && 
               reservationDate.getFullYear() === now.getFullYear()
      }).length || 0
    }

    return NextResponse.json({
      service,
      stats
    })
  } catch (error) {
    console.error('Service GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:read')

// PUT /api/admin/services/[id] - Update single service
export const PUT = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id
    const updates: ServiceUpdate = await request.json()

    // Get current service for audit log
    const { data: currentService } = await supabaseAdmin
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single()

    if (!currentService) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 })
    }

    // Update service
    const { data: updatedService, error } = await supabaseAdmin
      .from('services')
      .update(updates)
      .eq('id', serviceId)
      .select()
      .single()

    if (error) {
      console.error('Error updating service:', error)
      return NextResponse.json({ error: 'Failed to update service' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'UPDATE',
      'services',
      serviceId,
      currentService,
      updatedService,
      request
    )

    return NextResponse.json({ service: updatedService })
  } catch (error) {
    console.error('Service PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:write')

// DELETE /api/admin/services/[id] - Delete single service
export const DELETE = withAdminAuth(async (request: NextRequest, user, { params }: { params: { id: string } }) => {
  try {
    const serviceId = params.id

    // Get service for audit log
    const { data: serviceToDelete } = await supabaseAdmin
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single()

    if (!serviceToDelete) {
      return NextResponse.json({ error: 'Service not found' }, { status: 404 })
    }

    // Check for existing future reservations
    const { data: futureReservations } = await supabaseAdmin
      .from('reservations')
      .select('id')
      .eq('service_id', serviceId)
      .gte('start_time', new Date().toISOString())

    if (futureReservations && futureReservations.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete service with future reservations',
          futureReservations: futureReservations.length
        },
        { status: 400 }
      )
    }

    // Soft delete by setting is_active to false
    const { error } = await supabaseAdmin
      .from('services')
      .update({ is_active: false })
      .eq('id', serviceId)

    if (error) {
      console.error('Error deleting service:', error)
      return NextResponse.json({ error: 'Failed to delete service' }, { status: 500 })
    }

    // Log admin action
    await logAdminAction(
      user.id,
      'DELETE',
      'services',
      serviceId,
      serviceToDelete,
      null,
      request
    )

    return NextResponse.json({ 
      message: 'Service deactivated successfully',
      serviceId
    })
  } catch (error) {
    console.error('Service DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'services:delete')
