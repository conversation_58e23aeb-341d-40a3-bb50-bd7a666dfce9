"use client";

import { services as initialServices } from "@/data/adminMockData";
import { Service } from "@/types/admin";
import { Clock, Edit, Plus, Save, Tag, Trash2, Upload, Users, X } from "lucide-react";
import { useState } from "react";
import Button from "./ui/Button";

const AdminServices = () => {
	const [services, setServices] = useState<Service[]>(initialServices);
	const [isEditing, setIsEditing] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editForm, setEditForm] = useState<Partial<Service>>({});

	const handleEdit = (service: Service) => {
		setIsEditing(service.id);
		setEditForm(service);
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			name: "",
			description: "",
			shortDescription: "",
			price: 0,
			duration: 120,
			category: "",
			capacity: 10,
			images: ["https://images.pexels.com/photos/1435075/pexels-photo-1435075.jpeg"],
			features: [],
			availableSlots: [],
		});
	};

	const handleSave = () => {
		if (isCreating) {
			const newService: Service = {
				...(editForm as Service),
				id: `service-${Date.now()}`,
				availableSlots: [],
			};
			setServices([...services, newService]);
			setIsCreating(false);
		} else if (isEditing) {
			setServices(services.map((s) => (s.id === isEditing ? { ...s, ...editForm } : s)));
			setIsEditing(null);
		}
		setEditForm({});
	};

	const handleCancel = () => {
		setIsEditing(null);
		setIsCreating(false);
		setEditForm({});
	};

	const handleDelete = (serviceId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer ce service ?")) {
			setServices(services.filter((s) => s.id !== serviceId));
			console.log("Service deleted:", serviceId);
		}
	};

	const handleInputChange = (field: keyof Service, value: any) => {
		setEditForm((prev) => ({ ...prev, [field]: value }));
	};

	const addFeature = () => {
		const features = editForm.features || [];
		setEditForm((prev) => ({ ...prev, features: [...features, ""] }));
	};

	const updateFeature = (index: number, value: string) => {
		const features = [...(editForm.features || [])];
		features[index] = value;
		setEditForm((prev) => ({ ...prev, features }));
	};

	const removeFeature = (index: number) => {
		const features = [...(editForm.features || [])];
		features.splice(index, 1);
		setEditForm((prev) => ({ ...prev, features }));
	};

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Services</h1>
					<p className="text-gray-600">Gérez vos excursions et activités</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouveau Service
				</Button>
			</div>

			{/* Create/Edit Form */}
			{(isCreating || isEditing) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un nouveau service" : "Modifier le service"}
					</h2>

					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Nom du service *</label>
								<input
									type="text"
									value={editForm.name || ""}
									onChange={(e) => handleInputChange("name", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Description courte *
								</label>
								<input
									type="text"
									value={editForm.shortDescription || ""}
									onChange={(e) => handleInputChange("shortDescription", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Description complète *
								</label>
								<textarea
									rows={4}
									value={editForm.description || ""}
									onChange={(e) => handleInputChange("description", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Prix (€) *</label>
									<input
										type="number"
										value={editForm.price || 0}
										onChange={(e) => handleInputChange("price", parseInt(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Durée (minutes) *
									</label>
									<input
										type="number"
										value={editForm.duration || 120}
										onChange={(e) => handleInputChange("duration", parseInt(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
									<input
										type="text"
										value={editForm.category || ""}
										onChange={(e) => handleInputChange("category", e.target.value)}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Capacité max *
									</label>
									<input
										type="number"
										value={editForm.capacity || 10}
										onChange={(e) => handleInputChange("capacity", parseInt(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>
						</div>

						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Image principale</label>
								<div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
									<Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
									<p className="text-sm text-gray-600">Cliquez pour télécharger une image</p>
									<p className="text-xs text-gray-500 mt-1">PNG, JPG jusqu'à 10MB</p>
								</div>
							</div>

							<div>
								<div className="flex justify-between items-center mb-2">
									<label className="block text-sm font-medium text-gray-700">
										Caractéristiques incluses
									</label>
									<button
										onClick={addFeature}
										className="text-emerald-600 hover:text-emerald-700 text-sm font-medium"
									>
										+ Ajouter
									</button>
								</div>
								<div className="space-y-2">
									{(editForm.features || []).map((feature, index) => (
										<div key={index} className="flex gap-2">
											<input
												type="text"
												value={feature}
												onChange={(e) => updateFeature(index, e.target.value)}
												className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
												placeholder="Ex: Guide expert"
											/>
											<button
												onClick={() => removeFeature(index)}
												className="p-2 text-red-600 hover:text-red-700"
											>
												<X className="h-4 w-4" />
											</button>
										</div>
									))}
								</div>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Règles de disponibilité
								</label>
								<div className="space-y-3 p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Temps de préparation</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>30 minutes</option>
											<option>1 heure</option>
											<option>2 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Réservation minimum</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Annulation gratuite</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={Save}>
							{isCreating ? "Créer le service" : "Sauvegarder"}
						</Button>
					</div>
				</div>
			)}

			{/* Services List */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Services ({services.length})</h2>
				</div>
				<div className="divide-y divide-gray-200">
					{services.map((service) => (
						<div key={service.id} className="p-6 flex items-center gap-4">
							<img
								src={service.images[0]}
								alt={service.name}
								className="w-16 h-16 object-cover rounded-lg"
							/>
							<div className="flex-1">
								<div>
									<h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
									<p className="text-gray-600 text-sm mt-1">{service.shortDescription}</p>
								</div>
								<div className="flex items-center gap-6 mt-3 text-sm text-gray-500">
									<span className="flex items-center gap-1">
										<Clock className="h-4 w-4" />
										{service.duration} min
									</span>
									<span className="flex items-center gap-1">
										<Users className="h-4 w-4" />
										{service.capacity} pers.
									</span>
									<span className="flex items-center gap-1">
										<Tag className="h-4 w-4" />
										{service.category}
									</span>
									<span className="text-2xl font-bold text-emerald-600">€{service.price}</span>
								</div>
							</div>
							<div className="flex gap-2">
								<Button variant="outline" size="sm" icon={Edit} onClick={() => handleEdit(service)}>
									Modifier
								</Button>
								<Button
									variant="outline"
									size="sm"
									icon={Trash2}
									onClick={() => handleDelete(service.id)}
									className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
								>
									Supprimer
								</Button>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default AdminServices;
