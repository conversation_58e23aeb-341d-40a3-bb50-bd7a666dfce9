import { createClient } from "@supabase/supabase-js";
import { supabaseConfig } from "./env";
import { Database } from "./types/database";

// Client for browser/frontend use
export const supabase = supabaseConfig.url && supabaseConfig.anonKey
	? createClient<Database>(supabaseConfig.url, supabaseConfig.anonKey)
	: null;

// Admin client for server-side operations (uses service role key)
// Only create on server side where service role key is available
export const supabaseAdmin =
	typeof window === "undefined"
		? createClient<Database>(supabaseConfig.url, supabaseConfig.serviceRoleKey, {
				auth: {
					autoRefreshToken: false,
					persistSession: false,
				},
		  })
		: null;

// Helper function to handle Supabase errors
export function handleSupabaseError(error: any) {
	console.error("Supabase error:", error);

	if (error?.message) {
		return error.message;
	}

	return "An unexpected error occurred";
}

// Helper function for server-side database operations
export async function executeQuery<T>(
	queryFn: () => Promise<{ data: T | null; error: any }>
): Promise<{ data: T | null; error: string | null }> {
	try {
		const { data, error } = await queryFn();

		if (error) {
			return { data: null, error: handleSupabaseError(error) };
		}

		return { data, error: null };
	} catch (err) {
		return { data: null, error: handleSupabaseError(err) };
	}
}
