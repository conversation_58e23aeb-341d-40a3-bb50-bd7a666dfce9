import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { logAdminAction } from '@/lib/admin-auth'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Authenticate with Supabase
    const { data: authData, error: authError } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password
    })

    if (authError || !authData.user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Get user profile to check role
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      )
    }

    // Check if user has admin or employee role
    if (!['admin', 'employee'].includes(profile.role)) {
      return NextResponse.json(
        { error: 'Access denied. Admin or employee role required.' },
        { status: 403 }
      )
    }

    // Log successful admin login
    await logAdminAction(
      authData.user.id,
      'LOGIN',
      'auth',
      null,
      null,
      {
        email: authData.user.email,
        role: profile.role,
        loginTime: new Date().toISOString()
      },
      request
    )

    // Return user data and session
    return NextResponse.json({
      user: {
        id: authData.user.id,
        email: authData.user.email,
        role: profile.role,
        profile: {
          first_name: profile.first_name,
          last_name: profile.last_name,
          phone: profile.phone
        }
      },
      session: authData.session,
      access_token: authData.session?.access_token
    })

  } catch (error) {
    console.error('Admin login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
