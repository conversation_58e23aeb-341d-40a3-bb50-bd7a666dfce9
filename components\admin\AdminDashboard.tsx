"use client";

import React from 'react';
import { TrendingUp, Users, Calendar, DollarSign, Activity, Clock } from 'lucide-react';

const AdminDashboard = () => {
  const stats = [
    {
      title: 'Réservations ce mois',
      value: '127',
      change: '+12%',
      changeType: 'positive',
      icon: Calendar
    },
    {
      title: '<PERSON><PERSON>re d\'affaires',
      value: '€8,450',
      change: '+8%',
      changeType: 'positive',
      icon: DollarSign
    },
    {
      title: 'Clients actifs',
      value: '89',
      change: '+5%',
      changeType: 'positive',
      icon: Users
    },
    {
      title: 'Taux d\'occupation',
      value: '78%',
      change: '-3%',
      changeType: 'negative',
      icon: TrendingUp
    }
  ];

  const recentBookings = [
    {
      id: '1',
      customer: '<PERSON>',
      service: 'Découverte de la Mangrove',
      date: '2025-02-01',
      time: '09:00',
      status: 'confirmed',
      amount: '€90'
    },
    {
      id: '2',
      customer: '<PERSON>',
      service: '<PERSON><PERSON><PERSON> de Soleil en Catamaran',
      date: '2025-02-01',
      time: '17:30',
      status: 'pending',
      amount: '€340'
    },
    {
      id: '3',
      customer: '<PERSON>',
      service: 'Aventure en Waterbike',
      date: '2025-02-02',
      time: '10:00',
      status: 'confirmed',
      amount: '€70'
    }
  ];

  const upcomingActivities = [
    {
      time: '09:00',
      service: 'Découverte de la Mangrove',
      guide: 'Marie Dubois',
      participants: 4
    },
    {
      time: '14:00',
      service: 'Aventure en Waterbike',
      guide: 'Sophie Laroche',
      participants: 2
    },
    {
      time: '17:30',
      service: 'Coucher de Soleil en Catamaran',
      guide: 'Jean-Luc Martin',
      participants: 8
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Tableau de bord</h1>
        <p className="text-gray-600">Vue d'ensemble de votre activité</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="bg-emerald-100 p-2 rounded-lg">
                  <Icon className="h-6 w-6 text-emerald-600" />
                </div>
                <span className={`text-sm font-medium ${
                  stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.title}</p>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Bookings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Réservations récentes</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{booking.customer}</p>
                    <p className="text-sm text-gray-600">{booking.service}</p>
                    <p className="text-xs text-gray-500">{booking.date} à {booking.time}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-gray-900">{booking.amount}</p>
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      booking.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {booking.status === 'confirmed' ? 'Confirmé' : 'En attente'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Today's Activities */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold text-gray-900">Activités d'aujourd'hui</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {upcomingActivities.map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="bg-emerald-100 p-2 rounded-lg">
                    <Clock className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.time}</p>
                    <p className="text-sm text-gray-600">{activity.service}</p>
                    <p className="text-xs text-gray-500">Guide: {activity.guide}</p>
                  </div>
                  <div className="text-right">
                    <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                      {activity.participants} pers.
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Actions rapides</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 text-left bg-emerald-50 hover:bg-emerald-100 rounded-lg transition-colors">
            <Activity className="h-6 w-6 text-emerald-600 mb-2" />
            <p className="font-medium text-gray-900">Nouvelle réservation</p>
            <p className="text-sm text-gray-600">Créer une réservation manuelle</p>
          </button>
          <button className="p-4 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
            <Calendar className="h-6 w-6 text-blue-600 mb-2" />
            <p className="font-medium text-gray-900">Gérer le planning</p>
            <p className="text-sm text-gray-600">Modifier les créneaux disponibles</p>
          </button>
          <button className="p-4 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
            <Users className="h-6 w-6 text-purple-600 mb-2" />
            <p className="font-medium text-gray-900">Contacter un client</p>
            <p className="text-sm text-gray-600">Envoyer un message personnalisé</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
