import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { withAdminAuth, logAdminAction } from '@/lib/admin-auth'
import { Database } from '@/lib/types/database'

type Reservation = Database['public']['Tables']['reservations']['Row']
type ReservationUpdate = Database['public']['Tables']['reservations']['Update']

// GET /api/admin/reservations - List all reservations with admin details
export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const serviceId = searchParams.get('service_id')
    const customerId = searchParams.get('customer_id')
    const employeeId = searchParams.get('employee_id')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const search = searchParams.get('search')
    const offset = (page - 1) * limit

    // Build query
    let query = supabaseAdmin
      .from('reservations')
      .select(`
        *,
        service:services (
          id,
          name,
          duration_minutes,
          category,
          image_url
        ),
        customer:customers (
          id,
          emergency_contact_name,
          emergency_contact_phone,
          dietary_restrictions,
          medical_conditions
        ),
        customer_profile:profiles!reservations_customer_id_fkey (
          id,
          email,
          first_name,
          last_name,
          phone
        ),
        assigned_employee:employees (
          id,
          first_name,
          last_name,
          email,
          role
        ),
        payments (
          id,
          amount,
          currency,
          status,
          payment_method,
          payment_date
        ),
        customer_feedback (
          id,
          rating,
          review_text,
          service_quality_rating,
          staff_rating,
          equipment_rating,
          would_recommend
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status)
    }
    if (serviceId) {
      query = query.eq('service_id', serviceId)
    }
    if (customerId) {
      query = query.eq('customer_id', customerId)
    }
    if (employeeId) {
      query = query.eq('assigned_employee_id', employeeId)
    }
    if (dateFrom) {
      query = query.gte('start_time', dateFrom)
    }
    if (dateTo) {
      query = query.lte('start_time', dateTo)
    }
    if (search) {
      query = query.or(`reservation_number.ilike.%${search}%,special_requests.ilike.%${search}%`)
    }

    const { data: reservations, error } = await query

    if (error) {
      console.error('Error fetching reservations:', error)
      return NextResponse.json({ error: 'Failed to fetch reservations' }, { status: 500 })
    }

    // Get total count for pagination
    let countQuery = supabaseAdmin
      .from('reservations')
      .select('*', { count: 'exact', head: true })

    // Apply same filters to count query
    if (status && status !== 'all') {
      countQuery = countQuery.eq('status', status)
    }
    if (serviceId) {
      countQuery = countQuery.eq('service_id', serviceId)
    }
    if (customerId) {
      countQuery = countQuery.eq('customer_id', customerId)
    }
    if (employeeId) {
      countQuery = countQuery.eq('assigned_employee_id', employeeId)
    }
    if (dateFrom) {
      countQuery = countQuery.gte('start_time', dateFrom)
    }
    if (dateTo) {
      countQuery = countQuery.lte('start_time', dateTo)
    }

    const { count: totalCount } = await countQuery

    return NextResponse.json({
      reservations: reservations || [],
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      }
    })
  } catch (error) {
    console.error('Reservations GET error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:read')

// PUT /api/admin/reservations - Bulk update reservations
export const PUT = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { reservationIds, updates }: { reservationIds: string[], updates: ReservationUpdate } = await request.json()

    if (!reservationIds || reservationIds.length === 0) {
      return NextResponse.json({ error: 'No reservation IDs provided' }, { status: 400 })
    }

    // Get current reservations for audit log
    const { data: currentReservations } = await supabaseAdmin
      .from('reservations')
      .select('*')
      .in('id', reservationIds)

    // Add admin user info for confirmation actions
    if (updates.status === 'confirmed' && !updates.confirmed_by) {
      updates.confirmed_by = user.id
      updates.confirmed_at = new Date().toISOString()
    }

    // Update reservations
    const { data: updatedReservations, error } = await supabaseAdmin
      .from('reservations')
      .update(updates)
      .in('id', reservationIds)
      .select()

    if (error) {
      console.error('Error updating reservations:', error)
      return NextResponse.json({ error: 'Failed to update reservations' }, { status: 500 })
    }

    // Log admin actions
    for (const reservation of updatedReservations || []) {
      const oldReservation = currentReservations?.find(r => r.id === reservation.id)
      await logAdminAction(
        user.id,
        'UPDATE',
        'reservations',
        reservation.id,
        oldReservation,
        reservation,
        request
      )
    }

    return NextResponse.json({ 
      reservations: updatedReservations,
      updated: updatedReservations?.length || 0
    })
  } catch (error) {
    console.error('Reservations PUT error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:write')

// DELETE /api/admin/reservations - Cancel reservations
export const DELETE = withAdminAuth(async (request: NextRequest, user) => {
  try {
    const { reservationIds, reason }: { reservationIds: string[], reason?: string } = await request.json()

    if (!reservationIds || reservationIds.length === 0) {
      return NextResponse.json({ error: 'No reservation IDs provided' }, { status: 400 })
    }

    // Get reservations for audit log before cancellation
    const { data: reservationsToCancel } = await supabaseAdmin
      .from('reservations')
      .select('*')
      .in('id', reservationIds)

    // Cancel reservations (soft delete)
    const { error } = await supabaseAdmin
      .from('reservations')
      .update({ 
        status: 'cancelled',
        admin_notes: reason ? `Cancelled by admin: ${reason}` : 'Cancelled by admin'
      })
      .in('id', reservationIds)

    if (error) {
      console.error('Error cancelling reservations:', error)
      return NextResponse.json({ error: 'Failed to cancel reservations' }, { status: 500 })
    }

    // Log admin actions
    for (const reservation of reservationsToCancel || []) {
      await logAdminAction(
        user.id,
        'CANCEL',
        'reservations',
        reservation.id,
        reservation,
        { ...reservation, status: 'cancelled' },
        request
      )
    }

    return NextResponse.json({ 
      cancelled: reservationIds.length,
      message: 'Reservations cancelled successfully'
    })
  } catch (error) {
    console.error('Reservations DELETE error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}, 'reservations:write')
